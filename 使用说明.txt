龙头股技术分析程序使用说明
=====================================

程序功能：
---------
1. 读取stock_codes_with_prev_day.xlsx中的龙头股票池数据
2. 获取每只股票200个交易日的历史数据（以涨停前一天为结束日期）
3. 实现三种形态识别：
   a. 双顶形态识别（两个顶之间至少差距15个交易日）
   b. KDJ入场信号（连续放量上涨后KDJ死叉，KD差距连续2日缩小但未金叉）
   c. MACD背离识别（详细逻辑参考MACD_analysis.py）
4. 生成综合分析图表，包含K线、MACD、KDJ、MA均线和成交量
5. 保存图片到Data目录，保存分析结果到Excel文件

文件说明：
---------
main.py                    - 主程序文件（完整功能版本）
main_demo.py               - 演示程序（展示程序逻辑，不需要依赖包）
main_core.py               - 核心功能测试版本
MACD_analysis.py           - MACD背离分析逻辑参考
stock_codes_with_prev_day.xlsx - 股票池数据文件
install_requirements.py    - 依赖包安装脚本
requirements.txt           - 依赖包列表
README.md                  - 详细说明文档

使用步骤：
---------
1. 查看演示程序：
   python main_demo.py
   
2. 安装依赖包：
   python install_requirements.py
   或者手动安装：
   pip install pandas numpy matplotlib openpyxl baostock

3. 运行完整程序：
   python main.py

4. 查看结果：
   - 分析图表：Data目录下的PNG文件
   - 分析结果：当前目录下的Excel文件

Excel文件格式：
--------------
stock_codes_with_prev_day.xlsx应包含以下列：
- 股票代码：如600059
- 完整代码：如sh.600059  
- 第一次涨停日期：如2025-06-09
- 第一次涨停前一天：如2025-06-06（作为分析结束日期）

技术指标详细说明：
----------------

1. 双顶形态识别条件：
   - 寻找局部高点（前后2个交易日都比当前点低）
   - 两个顶点间隔≥15个交易日
   - 两个顶点价格相近（差距≤3%）
   - 中间有明显回调（≥5%的回调）

2. KDJ入场信号条件：
   - 前期有连续放量上涨（连续3天以上放量20%且上涨）
   - 出现KDJ死叉（K线下穿D线）
   - KD之间差距连续2日不断缩小
   - 但还未形成金叉（K仍小于D）

3. MACD背离识别条件：
   - 第一个峰值必须是倒数第3个或第4个MACD柱
   - MACD柱必须在零线下方
   - 寻找第二个峰值时，如果出现正MACD柱则停止分析
   - 检查背离：MACD上升但价格下降
   - 最新MACD柱绝对值相比前一天减少

输出结果：
---------
1. 控制台输出：
   - 每只股票的分析进度
   - 形态识别结果
   - 统计摘要

2. 图表文件（保存在Data目录）：
   - 文件名格式：股票代码_analysis.png
   - 包含4个子图：K线+MA、MACD、KDJ、成交量
   - 标记各种信号点和形态

3. Excel结果文件：
   - 文件名格式：龙头股分析结果_时间戳.xlsx
   - 包含所有股票的分析结果汇总

注意事项：
---------
1. 程序需要网络连接获取股票数据
2. 首次运行可能较慢，需要下载大量数据
3. 确保Excel文件在程序目录下且格式正确
4. Data目录会自动创建
5. 如遇到依赖包问题，请先运行install_requirements.py

故障排除：
---------
1. 如果pandas导入失败：pip install pandas
2. 如果baostock连接失败：检查网络连接
3. 如果Excel读取失败：检查文件格式和路径
4. 如果图表保存失败：检查磁盘空间和权限

技术支持：
---------
如有问题，请检查：
1. Python版本（建议3.7+）
2. 依赖包版本
3. 网络连接状态
4. 文件路径和权限
