# 龙头股技术分析程序

## 功能说明

本程序实现了对龙头股票池的技术分析，包括以下功能：

### 1. 形态识别功能
- **双顶形态识别**：识别股票K线双顶形态，两个顶之间至少差距15个交易日
- **KDJ入场信号**：连续一段放量上涨后出现KDJ死叉，KD之间的差距连续2日不断缩小，但还未形成金叉时的入场信号
- **MACD背离**：详细逻辑参考MACD_analysis.py，识别MACD背离信号

### 2. 数据获取
- 从stock_codes_with_prev_day.xlsx文件读取龙头股票池的股票代码和涨停前一天日期
- 获取每只股票50个交易日的历史数据作为分析基础

### 3. 图表绘制
在一个图中绘制：
- 股票K线图
- MACD图（标记符合MACD_analysis.py MACD背离逻辑的峰值和入场K线）
- KDJ图（标记放量上涨和死叉以及入场点）
- MA均线图
- 若有双顶形态，绘制连接两个双顶的横线

### 4. 结果保存
- 图片保存到：D:\量化投资\股票\股票测试数据\近期龙头股\龙头股分析\Data
- 分析结果保存为Excel文件
- 所有图表纵向对齐，便于对比分析

## 文件说明

- `main.py` - 主程序文件，包含完整的分析功能
- `main_core.py` - 核心功能测试版本
- `MACD_analysis.py` - MACD背离分析逻辑参考文件
- `stock_codes_with_prev_day.xlsx` - 股票池数据文件
- `install_requirements.py` - 依赖包安装脚本
- `requirements.txt` - 依赖包列表

## 使用方法

### 1. 安装依赖包
```bash
python install_requirements.py
```

或者手动安装：
```bash
pip install pandas numpy matplotlib openpyxl baostock
```

### 2. 运行程序
```bash
python main.py
```

### 3. 查看结果
- 分析图表保存在 `Data` 目录下
- 分析结果Excel文件保存在当前目录

## 数据文件格式

stock_codes_with_prev_day.xlsx 文件应包含以下列：
- 股票代码：如 600059
- 完整代码：如 sh.600059
- 第一次涨停日期：如 2025-06-09
- 第一次涨停前一天：如 2025-06-06（作为分析的结束日期）

## 技术指标说明

### MACD背离条件
1. 第一个峰值必须是倒数第3个或者第4个MACD柱
2. 在找到第一个峰值后，如果在检索第二个峰值过程中出现正的MACD柱，分析下一个股票
3. 在检测到两个峰值后，如果没有出现背离，分析下一个股票
4. 找到两个峰值且满足MACD背离条件，最新的MACD柱相比于前一天的MACD柱绝对值减少，才满足要求

### KDJ入场条件
1. 连续一段时间放量上涨（前期有连续3天以上放量20%且上涨）
2. 出现KDJ死叉（K线下穿D线）
3. KD之间的差距连续2日不断缩小
4. 但还未形成金叉（K仍小于D）

### 双顶形态条件
1. 两个顶点之间至少间隔15个交易日
2. 两个顶点价格相近（差距不超过3%）
3. 中间有明显回调（至少5%的回调）

## 注意事项

1. 程序需要网络连接以获取股票数据（使用baostock）
2. 首次运行可能需要较长时间下载数据
3. 确保stock_codes_with_prev_day.xlsx文件在程序目录下
4. 图片保存路径会自动创建，无需手动创建

## 故障排除

如果遇到问题：
1. 检查网络连接
2. 确认依赖包已正确安装
3. 检查Excel文件格式是否正确
4. 查看控制台输出的错误信息
