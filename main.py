import baostock as bs
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和负号显示
def set_chinese_font():
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False

def load_stock_codes():
    """
    读取股票代码和涨停前一天日期
    """
    try:
        df = pd.read_excel('stock_codes_with_prev_day.xlsx')
        print(f"成功读取股票池，共 {len(df)} 只股票")
        print(f"Excel文件列名: {df.columns.tolist()}")
        print(f"前3行数据:")
        print(df.head(3))
        return df
    except Exception as e:
        print(f"读取股票池文件失败: {e}")
        return None

def get_stock_data(stock_code, end_date, days=50):
    """
    获取股票数据，从end_date往前推days个交易日
    """
    try:
        print(f"正在获取 {stock_code} 数据...")
        lg = bs.login()
        
        if lg.error_code != '0':
            print(f"baostock登录失败: {lg.error_msg}")
            return None
        
        # 计算开始日期（往前推更多天数以确保有足够的交易日）
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        start_dt = end_dt - timedelta(days=days*2)  # 预留更多天数
        start_date = start_dt.strftime('%Y-%m-%d')
        
        rs = bs.query_history_k_data_plus(
            stock_code,
            "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
            start_date=start_date,
            end_date=end_date,
            frequency="d",
            adjustflag="2"
        )
        
        if rs.error_code != '0':
            print(f"查询股票 {stock_code} 失败: {rs.error_msg}")
            bs.logout()
            return None
        
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
        
        bs.logout()
        
        if not data_list:
            print(f"股票 {stock_code} 无数据")
            return None
        
        result = pd.DataFrame(data_list, columns=rs.fields)
        
        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn', 'pctChg']
        for col in numeric_columns:
            if col in result.columns:
                result[col] = pd.to_numeric(result[col], errors='coerce')
        
        # 只保留最近的days个交易日
        result = result.tail(days).reset_index(drop=True)
        
        print(f"获取完成，共 {len(result)} 条数据")
        return result
        
    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        try:
            bs.logout()
        except:
            pass
        return None

def calculate_macd(data, fast=12, slow=26, signal=9):
    """
    计算MACD指标
    """
    ema_fast = data['close'].ewm(span=fast).mean()
    ema_slow = data['close'].ewm(span=slow).mean()
    
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    macd_histogram = macd_line - signal_line
    
    return macd_line, signal_line, macd_histogram

def calculate_kdj(data, n=9, m1=3, m2=3):
    """
    计算KDJ指标
    """
    low_n = data['low'].rolling(window=n).min()
    high_n = data['high'].rolling(window=n).max()
    rsv = (data['close'] - low_n) / (high_n - low_n) * 100
    
    k = rsv.ewm(alpha=1/m1).mean()
    d = k.ewm(alpha=1/m2).mean()
    j = 3 * k - 2 * d
    
    return k, d, j

def calculate_ma(data, periods=[5, 10, 20, 60]):
    """
    计算移动平均线
    """
    ma_dict = {}
    for period in periods:
        ma_dict[f'MA{period}'] = data['close'].rolling(window=period).mean()
    return ma_dict

def detect_double_top(data, min_gap=15):
    """
    识别双顶形态
    返回: (是否有双顶, 第一个顶点日期, 第二个顶点日期, 连接线价格)
    """
    try:
        # 寻找局部高点
        highs = []
        for i in range(2, len(data)-2):
            if (data['high'].iloc[i] > data['high'].iloc[i-1] and 
                data['high'].iloc[i] > data['high'].iloc[i+1] and
                data['high'].iloc[i] > data['high'].iloc[i-2] and 
                data['high'].iloc[i] > data['high'].iloc[i+2]):
                highs.append((i, data['high'].iloc[i], data['date'].iloc[i]))
        
        if len(highs) < 2:
            return False, None, None, None
        
        # 寻找符合条件的双顶
        for i in range(len(highs)-1):
            for j in range(i+1, len(highs)):
                idx1, price1, date1 = highs[i]
                idx2, price2, date2 = highs[j]
                
                # 检查时间间隔
                if idx2 - idx1 >= min_gap:
                    # 检查价格相近（差距不超过3%）
                    price_diff = abs(price1 - price2) / max(price1, price2)
                    if price_diff <= 0.03:
                        # 检查中间是否有明显回调
                        min_between = data['low'].iloc[idx1:idx2+1].min()
                        if min_between < min(price1, price2) * 0.95:  # 至少5%的回调
                            connect_price = (price1 + price2) / 2
                            return True, date1, date2, connect_price
        
        return False, None, None, None
        
    except Exception as e:
        print(f"双顶识别失败: {e}")
        return False, None, None, None

def detect_kdj_entry(data):
    """
    识别KDJ入场信号：连续一段放量上涨后出现KDJ死叉，KD之间的差距连续2日不断缩小，但还未形成金叉
    返回: (是否有信号, 入场日期, 相关参数)
    """
    try:
        k, d, j = calculate_kdj(data)
        
        # 计算成交量均值用于判断放量
        vol_ma = data['volume'].rolling(window=20).mean()
        
        signals = []
        
        for i in range(30, len(data)-2):  # 留出足够的历史数据
            # 1. 检查前期是否有放量上涨
            volume_surge = False
            price_rise = False
            
            # 检查前10天是否有连续放量上涨
            for start in range(max(0, i-15), i-5):
                surge_days = 0
                rise_days = 0
                for day in range(start, start+5):
                    if day < len(data) and day >= 0:
                        if data['volume'].iloc[day] > vol_ma.iloc[day] * 1.2:  # 放量20%以上
                            surge_days += 1
                        if data['close'].iloc[day] > data['close'].iloc[day-1]:
                            rise_days += 1
                
                if surge_days >= 3 and rise_days >= 3:
                    volume_surge = True
                    price_rise = True
                    break
            
            if not (volume_surge and price_rise):
                continue
            
            # 2. 检查KDJ死叉
            if i >= 1 and k.iloc[i-1] > d.iloc[i-1] and k.iloc[i] < d.iloc[i]:
                # 3. 检查KD差距连续2日缩小但未金叉
                if i+2 < len(data):
                    kd_diff_1 = abs(k.iloc[i] - d.iloc[i])
                    kd_diff_2 = abs(k.iloc[i+1] - d.iloc[i+1])
                    kd_diff_3 = abs(k.iloc[i+2] - d.iloc[i+2])
                    
                    if (kd_diff_2 < kd_diff_1 and kd_diff_3 < kd_diff_2 and 
                        k.iloc[i+2] < d.iloc[i+2]):  # 还未金叉
                        
                        entry_date = data['date'].iloc[i+2]
                        signals.append({
                            'entry_date': entry_date,
                            'entry_price': data['close'].iloc[i+2],
                            'k_value': k.iloc[i+2],
                            'd_value': d.iloc[i+2],
                            'death_cross_date': data['date'].iloc[i]
                        })
        
        if signals:
            return True, signals[-1]['entry_date'], signals[-1]
        else:
            return False, None, None
            
    except Exception as e:
        print(f"KDJ信号识别失败: {e}")
        return False, None, None

def detect_macd_divergence(data):
    """
    使用MACD_analysis.py中的MACD背离逻辑
    """
    try:
        # 计算技术指标
        macd_line, signal_line, macd_histogram = calculate_macd(data)
        
        # 添加指标到数据中
        data_copy = data.copy()
        data_copy['macd_line'] = macd_line
        data_copy['signal_line'] = signal_line
        data_copy['macd_histogram'] = macd_histogram
        
        signals = []
        
        # 寻找MACD在零线下方的波峰（从最新数据开始检索）
        first_peak_found = False
        first_peak_idx = None
        
        for i in [3, 4]:  # 只检查倒数第3个和第4个MACD柱
            if i >= len(data_copy):
                continue
            
            # 确保MACD柱在零线下方
            if data_copy['macd_histogram'].iloc[-i] >= 0:
                continue
            
            # 检查是否为波峰（局部最小值）
            if (i >= 2 and i < len(data_copy) - 2 and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i-1] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i+1] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i-2] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i+2]):
                
                first_peak_found = True
                first_peak_idx = i
                break
        
        if not first_peak_found:
            return False, None, None
        
        # 寻找第二个波峰
        second_peak_found = False
        prev_peak_idx = None
        
        for j in range(first_peak_idx + 2, min(first_peak_idx + 50, len(data_copy) - 3)):
            # 检查是否出现正的MACD柱
            if data_copy['macd_histogram'].iloc[-j] > 0:
                return False, None, None
            
            if (data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j-1] and
                data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j+1] and
                data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j-2] and
                data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j+2] and
                data_copy['macd_histogram'].iloc[-j] < 0):
                prev_peak_idx = j
                second_peak_found = True
                break
        
        if not second_peak_found:
            return False, None, None
        
        # 检查背离条件
        current_macd = data_copy['macd_histogram'].iloc[-first_peak_idx]
        prev_macd = data_copy['macd_histogram'].iloc[-prev_peak_idx]
        current_price = data_copy['low'].iloc[-first_peak_idx]
        prev_price = data_copy['low'].iloc[-prev_peak_idx]
        
        if not (current_macd > prev_macd and current_price < prev_price):
            return False, None, None
        
        # 检查最新MACD柱绝对值是否减少
        if len(data_copy) < 2:
            return False, None, None
        
        latest_macd_abs = abs(data_copy['macd_histogram'].iloc[-1])
        prev_day_macd_abs = abs(data_copy['macd_histogram'].iloc[-2])
        
        if latest_macd_abs >= prev_day_macd_abs:
            return False, None, None
        
        # 生成信号
        entry_idx = len(data_copy) - first_peak_idx
        entry_date = data_copy['date'].iloc[entry_idx]
        
        signal_info = {
            'entry_date': entry_date,
            'entry_price': data_copy['close'].iloc[entry_idx],
            'first_peak_idx': len(data_copy) - first_peak_idx,
            'second_peak_idx': len(data_copy) - prev_peak_idx,
            'macd_line': data_copy['macd_line'].iloc[entry_idx],
            'macd_histogram': data_copy['macd_histogram'].iloc[entry_idx]
        }
        
        return True, entry_date, signal_info

    except Exception as e:
        print(f"MACD背离识别失败: {e}")
        return False, None, None

def plot_stock_analysis(data, stock_code, double_top_info, kdj_info, macd_info, save_path):
    """
    绘制股票分析图表
    """
    try:
        set_chinese_font()

        # 计算技术指标
        macd_line, signal_line, macd_histogram = calculate_macd(data)
        k, d, j = calculate_kdj(data)
        ma_dict = calculate_ma(data)

        # 创建子图 - 4个子图纵向排列，确保横轴对齐
        fig, (ax1, ax2, ax3, ax4) = plt.subplots(4, 1, figsize=(16, 20), sharex=True)
        fig.suptitle(f'{stock_code} 技术分析图表', fontsize=16, fontweight='bold')

        # 1. K线图和MA均线
        ax1.set_title('K线图与移动平均线')

        # 绘制K线
        for i in range(len(data)):
            color = 'red' if data['close'].iloc[i] >= data['open'].iloc[i] else 'green'
            # 实体
            ax1.plot([i, i], [data['open'].iloc[i], data['close'].iloc[i]],
                    color=color, linewidth=3)
            # 影线
            ax1.plot([i, i], [data['low'].iloc[i], data['high'].iloc[i]],
                    color=color, linewidth=1)

        # 绘制MA均线
        colors = ['blue', 'orange', 'purple', 'brown']
        for idx, (ma_name, ma_values) in enumerate(ma_dict.items()):
            ax1.plot(range(len(data)), ma_values, label=ma_name,
                    color=colors[idx % len(colors)], linewidth=1)

        # 绘制双顶连接线
        if double_top_info[0]:  # 如果有双顶
            _, date1, date2, connect_price = double_top_info
            try:
                idx1 = data[data['date'] == date1].index[0]
                idx2 = data[data['date'] == date2].index[0]
                ax1.axhline(y=connect_price, xmin=idx1/len(data), xmax=idx2/len(data),
                           color='red', linestyle='--', linewidth=2, label='双顶连接线')
                ax1.scatter([idx1, idx2], [connect_price, connect_price],
                           color='red', s=100, marker='v', zorder=5)
            except:
                pass

        # 标记KDJ入场点
        if kdj_info[0]:  # 如果有KDJ信号
            _, entry_date, kdj_params = kdj_info
            try:
                entry_idx = data[data['date'] == entry_date].index[0]
                ax1.scatter(entry_idx, data['close'].iloc[entry_idx],
                           color='yellow', s=150, marker='^',
                           edgecolors='black', linewidth=2, label='KDJ入场点', zorder=5)
            except:
                pass

        # 标记MACD入场点
        if macd_info[0]:  # 如果有MACD信号
            _, entry_date, macd_params = macd_info
            try:
                entry_idx = data[data['date'] == entry_date].index[0]
                ax1.scatter(entry_idx, data['close'].iloc[entry_idx],
                           color='cyan', s=150, marker='s',
                           edgecolors='black', linewidth=2, label='MACD入场点', zorder=5)
            except:
                pass

        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. MACD图
        ax2.set_title('MACD指标')
        ax2.plot(range(len(data)), macd_line, label='MACD线', color='blue', linewidth=1)
        ax2.plot(range(len(data)), signal_line, label='信号线', color='red', linewidth=1)
        ax2.bar(range(len(data)), macd_histogram, label='MACD柱',
                color=['red' if x > 0 else 'green' for x in macd_histogram], alpha=0.6)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=1)

        # 标记MACD背离峰值和入场点
        if macd_info[0]:
            _, entry_date, macd_params = macd_info
            try:
                # 标记两个峰值
                peak1_idx = macd_params['first_peak_idx']
                peak2_idx = macd_params['second_peak_idx']
                ax2.scatter([peak1_idx, peak2_idx],
                           [macd_histogram.iloc[peak1_idx], macd_histogram.iloc[peak2_idx]],
                           color='orange', s=100, marker='o',
                           edgecolors='black', linewidth=2, label='MACD峰值', zorder=5)

                # 标记入场点
                entry_idx = data[data['date'] == entry_date].index[0]
                ax2.scatter(entry_idx, macd_line.iloc[entry_idx],
                           color='cyan', s=150, marker='s',
                           edgecolors='black', linewidth=2, label='MACD入场', zorder=5)
            except:
                pass

        ax2.set_ylabel('MACD值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. KDJ图
        ax3.set_title('KDJ指标')
        ax3.plot(range(len(data)), k, label='K', color='blue', linewidth=1)
        ax3.plot(range(len(data)), d, label='D', color='red', linewidth=1)
        ax3.plot(range(len(data)), j, label='J', color='green', linewidth=1)
        ax3.axhline(y=80, color='red', linestyle='--', alpha=0.5, label='超买线(80)')
        ax3.axhline(y=20, color='green', linestyle='--', alpha=0.5, label='超卖线(20)')

        # 标记KDJ死叉和入场点
        if kdj_info[0]:
            _, entry_date, kdj_params = kdj_info
            try:
                # 标记死叉点
                death_cross_date = kdj_params['death_cross_date']
                death_idx = data[data['date'] == death_cross_date].index[0]
                ax3.scatter(death_idx, k.iloc[death_idx],
                           color='red', s=100, marker='x',
                           linewidth=3, label='KDJ死叉', zorder=5)

                # 标记入场点
                entry_idx = data[data['date'] == entry_date].index[0]
                ax3.scatter(entry_idx, k.iloc[entry_idx],
                           color='yellow', s=150, marker='^',
                           edgecolors='black', linewidth=2, label='KDJ入场', zorder=5)
            except:
                pass

        ax3.set_ylabel('KDJ值')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 成交量图
        ax4.set_title('成交量')
        colors = ['red' if data['close'].iloc[i] >= data['open'].iloc[i] else 'green'
                 for i in range(len(data))]
        ax4.bar(range(len(data)), data['volume'], color=colors, alpha=0.7)

        # 标记放量上涨区间
        if kdj_info[0]:
            vol_ma = data['volume'].rolling(window=20).mean()
            for i in range(len(data)):
                if data['volume'].iloc[i] > vol_ma.iloc[i] * 1.2:
                    ax4.bar(i, data['volume'].iloc[i], color='orange', alpha=0.8)

        ax4.set_ylabel('成交量')
        ax4.set_xlabel('时间')
        ax4.grid(True, alpha=0.3)

        # 设置x轴标签 - 只在最下面的子图显示
        x_ticks = range(0, len(data), max(1, len(data)//8))
        x_labels = [data['date'].iloc[i] for i in x_ticks]

        # 只在最下面的子图(ax4)显示x轴标签
        ax4.set_xticks(x_ticks)
        ax4.set_xticklabels(x_labels, rotation=45)

        # 其他子图隐藏x轴标签但保持网格
        for ax in [ax1, ax2, ax3]:
            ax.set_xticks(x_ticks)
            ax.set_xticklabels([])  # 隐藏标签但保持刻度

        plt.tight_layout()

        # 保存图片
        filename = f"{stock_code}_analysis.png"
        filepath = os.path.join(save_path, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"图表已保存: {filepath}")
        return True

    except Exception as e:
        print(f"绘图失败 {stock_code}: {e}")
        return False

def analyze_single_stock(stock_code, end_date):
    """
    分析单只股票
    """
    print(f"\n{'='*50}")
    print(f"开始分析股票: {stock_code}")
    print(f"分析截止日期: {end_date}")
    print(f"{'='*50}")

    # 获取股票数据
    data = get_stock_data(stock_code, end_date, days=50)
    if data is None or len(data) < 50:
        print(f"股票 {stock_code} 数据不足，跳过分析")
        return None

    # 形态识别
    results = {
        'stock_code': stock_code,
        'end_date': end_date,
        'data_length': len(data)
    }

    # 1. 双顶识别
    print("正在识别双顶形态...")
    double_top_info = detect_double_top(data, min_gap=15)
    results['double_top'] = {
        'has_pattern': double_top_info[0],
        'first_top_date': double_top_info[1],
        'second_top_date': double_top_info[2],
        'connect_price': double_top_info[3]
    }

    if double_top_info[0]:
        print(f"发现双顶形态: {double_top_info[1]} 和 {double_top_info[2]}")
    else:
        print("未发现双顶形态")

    # 2. KDJ入场信号识别
    print("正在识别KDJ入场信号...")
    kdj_info = detect_kdj_entry(data)
    results['kdj_entry'] = {
        'has_signal': kdj_info[0],
        'entry_date': kdj_info[1],
        'signal_params': kdj_info[2] if kdj_info[0] else None
    }

    if kdj_info[0]:
        print(f"发现KDJ入场信号: {kdj_info[1]}")
    else:
        print("未发现KDJ入场信号")

    # 3. MACD背离识别
    print("正在识别MACD背离...")
    macd_info = detect_macd_divergence(data)
    results['macd_divergence'] = {
        'has_signal': macd_info[0],
        'entry_date': macd_info[1],
        'signal_params': macd_info[2] if macd_info[0] else None
    }

    if macd_info[0]:
        print(f"发现MACD背离信号: {macd_info[1]}")
    else:
        print("未发现MACD背离信号")

    # 4. 绘制图表
    print("正在生成分析图表...")
    save_path = r"D:\量化投资\股票\股票测试数据\近期龙头股\龙头股分析\Data"
    os.makedirs(save_path, exist_ok=True)

    plot_success = plot_stock_analysis(data, stock_code, double_top_info, kdj_info, macd_info, save_path)
    results['plot_saved'] = plot_success

    return results

def main():
    """
    主函数：批量分析龙头股票池
    """
    print("龙头股技术分析程序启动")
    print("="*60)

    # 1. 读取股票代码和日期
    stock_df = load_stock_codes()
    if stock_df is None:
        print("无法读取股票池文件，程序退出")
        return

    # 2. 创建结果存储
    all_results = []
    summary_stats = {
        'total_stocks': len(stock_df),
        'analyzed_stocks': 0,
        'double_top_count': 0,
        'kdj_signal_count': 0,
        'macd_signal_count': 0,
        'any_signal_count': 0
    }

    # 3. 批量分析股票
    for index, row in stock_df.iterrows():
        try:
            # 获取股票代码和结束日期
            # 使用完整代码列，如果没有则使用股票代码列
            if '完整代码' in stock_df.columns:
                stock_code = str(row['完整代码'])
            elif '股票代码' in stock_df.columns:
                stock_code = str(row['股票代码'])
            else:
                stock_code = str(row.iloc[0])  # 使用第一列

            # 使用第一次涨停前一天作为分析结束日期
            if '第一次涨停前一天' in stock_df.columns:
                end_date = str(row['第一次涨停前一天'])
            elif '涨停前一天' in stock_df.columns:
                end_date = str(row['涨停前一天'])
            else:
                end_date = str(row.iloc[-1])  # 使用最后一列

            # 确保股票代码格式正确
            if stock_code.isdigit() and len(stock_code) < 6:
                stock_code = stock_code.zfill(6)

            if not stock_code.startswith(('sz.', 'sh.')):
                if stock_code.startswith(('0', '3')):
                    stock_code = f"sz.{stock_code}"
                elif stock_code.startswith('6'):
                    stock_code = f"sh.{stock_code}"

            # 确保日期格式正确
            if len(end_date) == 8 and end_date.isdigit():
                end_date = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

            print(f"\n进度: {index+1}/{len(stock_df)}")

            # 分析股票
            result = analyze_single_stock(stock_code, end_date)

            if result is not None:
                all_results.append(result)
                summary_stats['analyzed_stocks'] += 1

                # 统计信号数量
                if result['double_top']['has_pattern']:
                    summary_stats['double_top_count'] += 1

                if result['kdj_entry']['has_signal']:
                    summary_stats['kdj_signal_count'] += 1

                if result['macd_divergence']['has_signal']:
                    summary_stats['macd_signal_count'] += 1

                if (result['double_top']['has_pattern'] or
                    result['kdj_entry']['has_signal'] or
                    result['macd_divergence']['has_signal']):
                    summary_stats['any_signal_count'] += 1

        except Exception as e:
            print(f"分析股票 {stock_code} 时发生错误: {e}")
            continue

    # 4. 保存分析结果
    if all_results:
        # 创建结果DataFrame
        results_summary = []
        for result in all_results:
            summary_row = {
                '股票代码': result['stock_code'],
                '分析截止日期': result['end_date'],
                '数据长度': result['data_length'],
                '双顶形态': '是' if result['double_top']['has_pattern'] else '否',
                '双顶日期1': result['double_top']['first_top_date'],
                '双顶日期2': result['double_top']['second_top_date'],
                'KDJ入场信号': '是' if result['kdj_entry']['has_signal'] else '否',
                'KDJ入场日期': result['kdj_entry']['entry_date'],
                'MACD背离信号': '是' if result['macd_divergence']['has_signal'] else '否',
                'MACD入场日期': result['macd_divergence']['entry_date'],
                '图表已保存': '是' if result['plot_saved'] else '否'
            }
            results_summary.append(summary_row)

        # 保存到Excel
        results_df = pd.DataFrame(results_summary)
        output_file = f"龙头股分析结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        results_df.to_excel(output_file, index=False)
        print(f"\n分析结果已保存到: {output_file}")

    # 5. 打印统计摘要
    print("\n" + "="*60)
    print("分析完成！统计摘要:")
    print("="*60)
    print(f"总股票数量: {summary_stats['total_stocks']}")
    print(f"成功分析数量: {summary_stats['analyzed_stocks']}")
    print(f"发现双顶形态: {summary_stats['double_top_count']} 只")
    print(f"发现KDJ信号: {summary_stats['kdj_signal_count']} 只")
    print(f"发现MACD信号: {summary_stats['macd_signal_count']} 只")
    print(f"至少有一个信号: {summary_stats['any_signal_count']} 只")
    print(f"图表保存路径: D:\\量化投资\\股票\\股票测试数据\\近期龙头股\\龙头股分析\\Data")
    print("="*60)

if __name__ == "__main__":
    main()
