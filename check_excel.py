import os
print("当前目录:", os.getcwd())
print("文件列表:")
for f in os.listdir('.'):
    print(f"  {f}")

# 检查Excel文件是否存在
excel_file = 'stock_codes_with_prev_day.xlsx'
if os.path.exists(excel_file):
    print(f"\n✓ 找到Excel文件: {excel_file}")
    print(f"文件大小: {os.path.getsize(excel_file)} 字节")
else:
    print(f"\n✗ 未找到Excel文件: {excel_file}")

# 尝试使用openpyxl读取
try:
    from openpyxl import load_workbook
    print("\n尝试使用openpyxl读取...")
    wb = load_workbook(excel_file)
    ws = wb.active
    print(f"工作表名称: {ws.title}")
    print(f"最大行数: {ws.max_row}")
    print(f"最大列数: {ws.max_column}")
    
    # 读取前几行数据
    print("\n前5行数据:")
    for row in range(1, min(6, ws.max_row + 1)):
        row_data = []
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=row, column=col).value
            row_data.append(cell_value)
        print(f"第{row}行: {row_data}")
        
except ImportError:
    print("openpyxl 未安装")
except Exception as e:
    print(f"读取Excel文件失败: {e}")
    import traceback
    traceback.print_exc()
