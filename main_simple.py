#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("程序开始运行...")

try:
    import pandas as pd
    print("✓ pandas 导入成功")
except ImportError as e:
    print(f"✗ pandas 导入失败: {e}")
    exit(1)

try:
    import numpy as np
    print("✓ numpy 导入成功")
except ImportError as e:
    print(f"✗ numpy 导入失败: {e}")
    exit(1)

try:
    import matplotlib.pyplot as plt
    print("✓ matplotlib 导入成功")
except ImportError as e:
    print(f"✗ matplotlib 导入失败: {e}")
    exit(1)

try:
    import baostock as bs
    print("✓ baostock 导入成功")
except ImportError as e:
    print(f"✗ baostock 导入失败: {e}")
    exit(1)

import os
from datetime import datetime, timedelta

print("所有依赖包导入成功！")

# 测试读取Excel文件
print("\n测试读取Excel文件...")
try:
    df = pd.read_excel('stock_codes_with_prev_day.xlsx')
    print(f"✓ 成功读取Excel文件，共 {len(df)} 行数据")
    print(f"列名: {df.columns.tolist()}")
    print("前3行数据:")
    print(df.head(3))
except Exception as e:
    print(f"✗ 读取Excel文件失败: {e}")
    import traceback
    traceback.print_exc()

print("\n基础测试完成！")
