#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
安装必要的依赖包
"""

import subprocess
import sys

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("开始安装依赖包...")
    print("="*50)
    
    packages = [
        "pandas>=1.3.0",
        "numpy>=1.21.0", 
        "matplotlib>=3.4.0",
        "openpyxl>=3.0.0",
        "baostock>=0.8.8"
    ]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("="*50)
    print(f"安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("✓ 所有依赖包安装成功，可以运行main.py")
    else:
        print("✗ 部分依赖包安装失败，请手动安装")

if __name__ == "__main__":
    main()
