龙头股技术分析程序更新说明
==============================

更新时间：2025-07-27
更新版本：v1.1

主要更新内容：
=============

1. 数据长度调整
   - 分析数据长度从200个交易日调整为50个交易日
   - 提高分析效率，聚焦近期走势
   - 减少数据获取时间

2. 图表布局优化
   - 将4个子图从2x2布局改为4x1纵向布局
   - K线图、MACD图、KDJ图、成交量图完全纵向对齐
   - 横轴时间刻度完全对应，便于对比分析
   - 只在最下方显示时间标签，减少视觉干扰

3. 图表尺寸调整
   - 图表尺寸从20x16调整为16x20
   - 更适合纵向布局的显示效果
   - 保证每个子图有足够的显示空间

4. 文档更新
   - 更新所有相关文档中的数据长度说明
   - 更新图表布局描述
   - 保持文档与代码的一致性

具体修改文件：
=============

1. main.py
   - get_stock_data函数默认参数：days=200 → days=50
   - analyze_single_stock函数调用：days=200 → days=50
   - plot_stock_analysis函数：
     * 子图布局：2x2 → 4x1
     * 图表尺寸：(20,16) → (16,20)
     * 添加sharex=True实现横轴对齐
     * 优化x轴标签显示逻辑

2. main_demo.py
   - 演示文本中的数据长度说明：200 → 50

3. main_core.py
   - get_stock_data函数默认参数：days=200 → days=50

4. README.md
   - 数据获取说明：200个交易日 → 50个交易日
   - 添加图表对齐说明

5. 使用说明.txt
   - 数据长度说明：200个交易日 → 50个交易日
   - 图表布局说明：添加纵向对齐描述

技术改进：
=========

1. 图表对齐机制
   - 使用sharex=True参数确保所有子图横轴对齐
   - 统一x轴刻度设置
   - 只在底部子图显示时间标签

2. 性能优化
   - 减少数据获取量，提高运行速度
   - 保持分析精度的同时提升效率

3. 用户体验
   - 图表更易于阅读和对比
   - 时间轴完全对应，便于分析
   - 减少不必要的标签重复

使用建议：
=========

1. 50个交易日的数据长度适合：
   - 短期技术分析
   - 近期形态识别
   - 快速批量分析

2. 如需更长期分析，可以手动修改：
   - main.py中get_stock_data函数的days参数
   - analyze_single_stock函数调用中的days参数

3. 图表查看建议：
   - 利用纵向对齐特性对比各指标
   - 重点关注时间节点的一致性
   - 结合多个指标进行综合判断

兼容性说明：
===========

- 与之前版本完全兼容
- Excel文件格式无变化
- 所有功能保持不变
- 只是优化了显示效果和数据长度

后续计划：
=========

- 考虑添加数据长度配置选项
- 进一步优化图表显示效果
- 增加更多技术指标选项
