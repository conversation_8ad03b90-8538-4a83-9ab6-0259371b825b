import baostock as bs
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import os

def get_date_range():
    """
    获取日期范围：最新日期往前60天
    """
    # 获取当前日期，但只取工作日
    end_date = datetime.now()
    # 如果是周末，调整到最近的工作日
    while end_date.weekday() > 4:  # 0-4是周一到周五，5-6是周六周日
        end_date = end_date - timedelta(days=1)

    start_date = end_date - timedelta(days=60)

    return start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')

def get_stock_data(stock_code, start_date, end_date):
    """
    获取单只股票数据
    """
    try:
        print(f"正在登录baostock获取 {stock_code} 数据...")
        lg = bs.login()

        if lg.error_code != '0':
            print(f"baostock登录失败: {lg.error_msg}")
            return None

        print(f"登录成功，查询股票数据...")
        rs = bs.query_history_k_data_plus(
            stock_code,
            "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
            start_date=start_date,
            end_date=end_date,
            frequency="d",
            adjustflag="2"
        )

        if rs.error_code != '0':
            print(f"查询股票 {stock_code} 失败: {rs.error_msg}")
            bs.logout()
            return None

        data_list = []
        count = 0
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
            count += 1
            if count % 100 == 0:
                print(f"已获取 {count} 条数据...")

        bs.logout()
        print(f"获取完成，共 {len(data_list)} 条数据")

        if not data_list:
            print(f"股票 {stock_code} 无数据")
            return None

        result = pd.DataFrame(data_list, columns=rs.fields)

        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn']
        for col in numeric_columns:
            if col in result.columns:
                result[col] = pd.to_numeric(result[col], errors='coerce')

        return result

    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        try:
            bs.logout()
        except:
            pass
        return None
    
def calculate_macd(data, fast=12, slow=26, signal=9):
    """
    计算MACD指标
    fast: 快线周期，默认12
    slow: 慢线周期，默认26
    signal: 信号线周期，默认9
    """
    ema_fast = data['close'].ewm(span=fast).mean()
    ema_slow = data['close'].ewm(span=slow).mean()
    
    # MACD线 = 快线EMA - 慢线EMA
    macd_line = ema_fast - ema_slow
    
    # 信号线 = MACD线的EMA
    signal_line = macd_line.ewm(span=signal).mean()
    
    # MACD柱 = MACD线 - 信号线
    macd_histogram = macd_line - signal_line
    
    return macd_line, signal_line, macd_histogram

def calculate_atr(data, period=14):
    """
    计算ATR指标（平均真实波幅）
    period: 计算周期，默认14
    """
    # 计算真实波幅TR
    high_low = data['high'] - data['low']
    high_close_prev = abs(data['high'] - data['close'].shift(1))
    low_close_prev = abs(data['low'] - data['close'].shift(1))
    
    tr = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
    
    # ATR = TR的移动平均
    atr = tr.rolling(window=period).mean()
    
    return atr

def find_macd_divergence_signals(data):
    """
    识别MACD背离信号和交易条件
    返回交易信号DataFrame

    修改后的检测条件：
    1. 第一个峰值必须是倒数第3个或者第4个MACD柱
    2. 在找到第一个峰值后，如果在检索第二个峰值过程中出现正的MACD柱，分析下一个股票
    3. 在检测到两个峰值后，如果没有出现背离，分析下一个股票
    4. 找到两个峰值且满足MACD背离条件，最新的MACD柱相比于前一天的MACD柱绝对值减少，才满足要求
    """
    # 计算技术指标
    macd_line, signal_line, macd_histogram = calculate_macd(data)
    atr = calculate_atr(data)

    # 添加指标到数据中
    data_copy = data.copy()
    data_copy['macd_line'] = macd_line
    data_copy['signal_line'] = signal_line
    data_copy['macd_histogram'] = macd_histogram
    data_copy['atr'] = atr

    # print("macd_histogram")
    # print(macd_histogram)

    signals = []

    # 寻找MACD在零线下方的波峰（从最新数据开始检索）
    # 修改后的条件：第一个峰值必须是倒数第3个或者第4个MACD柱
    first_peak_found = False
    first_peak_idx = None

    for i in [3, 4]:  # 只检查倒数第3个和第4个MACD柱
        if i >= len(data_copy):
            continue

        # 确保MACD柱在零线下方
        if data_copy['macd_histogram'].iloc[-i] >= 0:
            continue

        # 检查是否为波峰（局部最小值），考虑前后两点
        # 需要确保索引不越界
        if (i >= 2 and i < len(data_copy) - 2 and
            data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i-1] and
            data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i+1] and
            data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i-2] and
            data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i+2]):

            print(f"找到第一个波峰，i={i}, 实际索引={len(data_copy)-i}")
            first_peak_found = True
            first_peak_idx = i
            break

    # 如果没有在倒数第3或第4个位置找到第一个峰值，返回空结果
    if not first_peak_found:
        print("未在倒数第3或第4个位置找到第一个波峰，停止分析")
        return pd.DataFrame(signals)

    # 寻找第二个波峰（更早的数据）
    second_peak_found = False
    prev_peak_idx = None

    # j的范围：从first_peak_idx+2开始搜索
    for j in range(first_peak_idx + 2, min(first_peak_idx + 50, len(data_copy) - 3)):
        # 新增条件：在检索第二个峰值过程中，如果出现正的MACD柱，停止分析
        if data_copy['macd_histogram'].iloc[-j] > 0:
            print(f"在检索第二个峰值时发现正MACD柱在索引{len(data_copy)-j}，停止分析")
            return pd.DataFrame(signals)

        if (data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j-1] and
            data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j+1] and
            data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j-2] and
            data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j+2] and
            data_copy['macd_histogram'].iloc[-j] < 0):
            prev_peak_idx = j
            second_peak_found = True
            print(f"找到第二个波峰，j={prev_peak_idx}, 实际索引={len(data_copy)-prev_peak_idx}")
            break

    # 如果没有找到第二个峰值，返回空结果
    if not second_peak_found:
        print("未找到第二个波峰，停止分析")
        return pd.DataFrame(signals)

    # 检查背离条件
    current_macd = data_copy['macd_histogram'].iloc[-first_peak_idx]
    prev_macd = data_copy['macd_histogram'].iloc[-prev_peak_idx]
    current_price = data_copy['low'].iloc[-first_peak_idx]
    prev_price = data_copy['low'].iloc[-prev_peak_idx]

    # 检查是否满足背离条件
    if not (current_macd > prev_macd and current_price < prev_price):
        print("未满足MACD背离条件，停止分析")
        return pd.DataFrame(signals)

    print("满足MACD背离条件")

    # 条件4：检查最新的MACD柱相比于前一天的MACD柱绝对值是否减少
    if len(data_copy) < 2:
        print("数据不足，无法比较最新MACD柱")
        return pd.DataFrame(signals)

    latest_macd_abs = abs(data_copy['macd_histogram'].iloc[-1])
    prev_day_macd_abs = abs(data_copy['macd_histogram'].iloc[-2])

    if latest_macd_abs >= prev_day_macd_abs:
        print(f"最新MACD柱绝对值({latest_macd_abs:.6f})未减少，前一天为({prev_day_macd_abs:.6f})，停止分析")
        return pd.DataFrame(signals)

    print(f"最新MACD柱绝对值({latest_macd_abs:.6f})相比前一天({prev_day_macd_abs:.6f})减少，满足条件")

    # 所有条件都满足，生成交易信号
    # 使用第一个峰值位置作为入场点
    entry_idx = len(data_copy) - first_peak_idx
    entry_signal = create_trade_signal(data_copy, entry_idx)

    if entry_signal:
        signals.append(entry_signal)
        print("生成交易信号")

    return pd.DataFrame(signals)

def save_stock_chart(stock_data, stock_code, signals, save_path):
    """
    保存股票图表到指定路径
    """
    try:
        set_chinese_font()
        # 转换日期格式
        data = stock_data.copy()
        data['date'] = pd.to_datetime(data['date'])
        data = data.set_index('date')

        # 计算技术指标
        k, d, j = calculate_kdj(data)
        ema12 = calculate_ema(data['close'], 12)
        ema26 = calculate_ema(data['close'], 26)
        macd_line, signal_line, macd_histogram = calculate_macd(data)

        # 创建子图
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), sharex=True)

        # 绘制K线图
        for i in range(len(data)):
            color = 'red' if data['close'].iloc[i] >= data['open'].iloc[i] else 'green'
            ax1.plot([i, i], [data['open'].iloc[i], data['close'].iloc[i]],
                    color=color, linewidth=3)
            ax1.plot([i, i], [data['low'].iloc[i], data['high'].iloc[i]],
                    color=color, linewidth=1)

        # 绘制EMA
        ax1.plot(range(len(data)), ema12, label='EMA12', color='blue', linewidth=1)
        ax1.plot(range(len(data)), ema26, label='EMA26', color='orange', linewidth=1)

        # 标记买入信号
        if not signals.empty:
            for _, signal in signals.iterrows():
                if isinstance(signal['date'], int):
                    entry_idx = signal['date']
                else:
                    try:
                        entry_idx = data.index.get_loc(pd.to_datetime(signal['date']))
                    except:
                        continue
                
                if 0 <= entry_idx < len(data):
                    ax1.scatter(entry_idx, signal['entry_price'], 
                               color='yellow', s=100, marker='^', 
                               edgecolors='black', linewidth=2, zorder=5)

        ax1.set_title(f'{stock_code} K线图与EMA指标及MACD买入信号')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 绘制KDJ指标
        ax2.plot(range(len(data)), k, label='K', color='blue', linewidth=1)
        ax2.plot(range(len(data)), d, label='D', color='red', linewidth=1)
        ax2.plot(range(len(data)), j, label='J', color='green', linewidth=1)
        ax2.set_title('KDJ指标')
        ax2.set_ylabel('KDJ值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 绘制MACD指标
        ax3.plot(range(len(data)), macd_line, label='MACD线', color='blue', linewidth=1)
        ax3.plot(range(len(data)), signal_line, label='信号线', color='red', linewidth=1)
        ax3.bar(range(len(data)), macd_histogram, label='MACD柱', 
                color=['red' if x > 0 else 'green' for x in macd_histogram], alpha=0.6)
        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=1)
        
        # 标记MACD买入信号点
        if not signals.empty:
            for _, signal in signals.iterrows():
                if isinstance(signal['date'], int):
                    entry_idx = signal['date']
                else:
                    try:
                        entry_idx = data.index.get_loc(pd.to_datetime(signal['date']))
                    except:
                        continue
                
                if 0 <= entry_idx < len(data):
                    ax3.scatter(entry_idx, signal['macd_line'], 
                               color='yellow', s=80, marker='^', 
                               edgecolors='black', linewidth=2, zorder=5)

        ax3.set_title('MACD指标')
        ax3.set_ylabel('MACD值')
        ax3.set_xlabel('时间')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 设置x轴标签
        x_ticks = range(0, len(data), max(1, len(data)//10))
        x_labels = [data.index[i].strftime('%Y-%m-%d') for i in x_ticks]
        ax3.set_xticks(x_ticks)
        ax3.set_xticklabels(x_labels, rotation=45)

        plt.tight_layout()
        
        # 保存图片
        filename = f"{stock_code}_MACD_analysis.png"
        filepath = os.path.join(save_path, filename)
        plt.savefig(filepath, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"图表已保存: {filepath}")
        
    except Exception as e:
        print(f"保存图表失败 {stock_code}: {e}")

def batch_analyze_stocks():
    """
    批量分析股票池中的股票
    """
    # 读取股票池
    try:
        stock_pool = pd.read_excel(r"D:\edge下载\量化文档\stock_pool.xlsx")
        print(f"读取到 {len(stock_pool)} 只股票")
    except Exception as e:
        print(f"读取股票池文件失败: {e}")
        return
    
    # 获取日期范围
    start_date, end_date = get_date_range()
    print(f"分析日期范围: {start_date} 到 {end_date}")
    
    # 创建保存目录
    fig_path = r"D:\python\MACD加ATR策略\Fig"
    os.makedirs(fig_path, exist_ok=True)
    
    # 存储满足条件的股票
    qualified_stocks = []
    all_signals = []
    
    # 遍历股票池
    for index, row in stock_pool.iterrows():
        # 尝试从不同可能的列名获取股票代码
        if 'code' in stock_pool.columns:
            stock_code = str(row['code'])
        elif 'stock_code' in stock_pool.columns:
            stock_code = str(row['stock_code'])
        elif '股票代码' in stock_pool.columns:
            stock_code = str(row['股票代码'])
        else:
            stock_code = str(row.iloc[0])  # 默认使用第一列

        # 确保股票代码是6位数字（补零）
        if stock_code.isdigit() and len(stock_code) < 6:
            stock_code = stock_code.zfill(6)

        # 确保股票代码格式正确（添加市场前缀）
        if not stock_code.startswith(('sz.', 'sh.')):
            if stock_code.startswith('0') or stock_code.startswith('3'):
                stock_code = f"sz.{stock_code}"
            elif stock_code.startswith('6'):
                stock_code = f"sh.{stock_code}"

        print(f"\n正在分析股票: {stock_code} ({index+1}/{len(stock_pool)})")
        
        # 获取股票数据
        stock_data = get_stock_data(stock_code, start_date, end_date)
        
        if stock_data is None or len(stock_data) < 30:
            print(f"股票 {stock_code} 数据不足，跳过")
            continue
        
        # 分析MACD信号
        try:
            trade_signals = find_macd_divergence_signals(stock_data)
            
            if not trade_signals.empty:
                print(f"股票 {stock_code} 发现 {len(trade_signals)} 个MACD背离信号")
                
                # 添加股票代码到信号中
                trade_signals['stock_code'] = stock_code
                all_signals.append(trade_signals)
                
                # 记录满足条件的股票
                qualified_stocks.append({
                    'stock_code': stock_code,
                    'signal_count': len(trade_signals),
                    'latest_signal_date': trade_signals['date'].iloc[-1] if len(trade_signals) > 0 else None,
                    'analysis_date': end_date
                })
                
                # 保存图表
                save_stock_chart(stock_data, stock_code, trade_signals, fig_path)
                
        except Exception as e:
            print(f"分析股票 {stock_code} 失败: {e}")
            continue
    
    # 保存结果
    if qualified_stocks:
        # 保存满足条件的股票列表到当前目录
        qualified_df = pd.DataFrame(qualified_stocks)
        qualified_filename = f"qualified_stocks_{end_date.replace('-', '')}.xlsx"
        qualified_filepath = os.path.join(os.getcwd(), qualified_filename)
        qualified_df.to_excel(qualified_filepath, index=False)
        print(f"\n满足条件的股票已保存到: {qualified_filepath}")

        # 保存所有交易信号
        if all_signals:
            all_signals_df = pd.concat(all_signals, ignore_index=True)
            signals_filename = f"all_trade_signals_{end_date.replace('-', '')}.xlsx"
            signals_filepath = os.path.join(os.getcwd(), signals_filename)
            all_signals_df.to_excel(signals_filepath, index=False)
            print(f"所有交易信号已保存到: {signals_filepath}")

        print(f"\n总结:")
        print(f"分析股票总数: {len(stock_pool)}")
        print(f"满足MACD条件股票数: {len(qualified_stocks)}")
        print(f"图表保存路径: {fig_path}")

    else:
        print("\n未发现满足MACD背离条件的股票")

# 执行批量分析
if __name__ == "__main__":
    batch_analyze_stocks()

def calculate_kdj(data, n=9, m1=3, m2=3):
    """
    计算KDJ指标
    n: RSV计算周期，默认9
    m1: K值平滑周期，默认3
    m2: D值平滑周期，默认3
    """
    # 计算RSV
    low_n = data['low'].rolling(window=n).min()
    high_n = data['high'].rolling(window=n).max()
    rsv = (data['close'] - low_n) / (high_n - low_n) * 100

    # 计算K值
    k = rsv.ewm(alpha=1/m1).mean()

    # 计算D值
    d = k.ewm(alpha=1/m2).mean()

    # 计算J值
    j = 3 * k - 2 * d

    return k, d, j

def calculate_ema(data, period):
    """
    计算EMA指标
    data: 价格数据
    period: 计算周期
    """
    return data.ewm(span=period).mean()

def create_trade_signal(data, entry_idx):
    """
    创建交易信号
    """
    if entry_idx >= len(data):
        return None
        
    entry_bar = data.iloc[entry_idx]
    
    # 入场价格：开盘价
    entry_price = entry_bar['open']
    
    # 止损：入场价格 - 0.1 * ATR
    stop_loss = entry_price - 0.1 * entry_bar['atr']
    
    # 止盈：入场K线实体的2.5倍
    body_size = abs(entry_bar['close'] - entry_bar['open'])
    take_profit = entry_price + 2.5 * body_size
    
    return {
        'date': entry_bar.name if hasattr(entry_bar, 'name') else entry_idx,
        'entry_price': entry_price,
        'stop_loss': stop_loss,
        'take_profit': take_profit,
        'atr': entry_bar['atr'],
        'body_size': body_size,
        'macd_line': entry_bar['macd_line'],
        'macd_histogram': entry_bar['macd_histogram']
    }

# 设置中文字体和负号显示
def set_chinese_font():
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    
def plot_kline_with_indicators_and_signals(data):
    """
    绘制K线图、技术指标和MACD买入信号
    """
    set_chinese_font()
    # 转换日期格式
    data['date'] = pd.to_datetime(data['date'])
    data = data.set_index('date')

    # 计算技术指标
    k, d, j = calculate_kdj(data)
    ema12 = calculate_ema(data['close'], 12)
    ema26 = calculate_ema(data['close'], 26)
    macd_line, signal_line, macd_histogram = calculate_macd(data)
    
    # 获取交易信号
    trade_signals = find_macd_divergence_signals(data.reset_index())
    print("trading_signals")
    print(trade_signals)

    # 创建子图
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), sharex=True)

    # 绘制K线图
    for i in range(len(data)):
        color = 'red' if data['close'].iloc[i] >= data['open'].iloc[i] else 'green'
        # 绘制实体
        ax1.plot([i, i], [data['open'].iloc[i], data['close'].iloc[i]],
                color=color, linewidth=3)
        # 绘制影线
        ax1.plot([i, i], [data['low'].iloc[i], data['high'].iloc[i]],
                color=color, linewidth=1)

    # 绘制EMA
    ax1.plot(range(len(data)), ema12, label='EMA12', color='blue', linewidth=1)
    ax1.plot(range(len(data)), ema26, label='EMA26', color='orange', linewidth=1)

    # 标记买入信号
    if not trade_signals.empty:
        for _, signal in trade_signals.iterrows():
            if isinstance(signal['date'], int):
                entry_idx = signal['date']
            else:
                try:
                    entry_idx = data.index.get_loc(pd.to_datetime(signal['date']))
                except:
                    continue
            
            if 0 <= entry_idx < len(data):
                ax1.scatter(entry_idx, signal['entry_price'], 
                           color='yellow', s=100, marker='^', 
                           edgecolors='black', linewidth=2, 
                           label='买入信号' if _ == 0 else "", zorder=5)
                
                # 添加止损止盈线
                ax1.axhline(y=signal['stop_loss'], color='red', 
                           linestyle=':', alpha=0.7, linewidth=1)
                ax1.axhline(y=signal['take_profit'], color='green', 
                           linestyle=':', alpha=0.7, linewidth=1)

    ax1.set_title('K线图与EMA指标及MACD买入信号')
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 绘制KDJ指标
    ax2.plot(range(len(data)), k, label='K', color='blue', linewidth=1)
    ax2.plot(range(len(data)), d, label='D', color='red', linewidth=1)
    ax2.plot(range(len(data)), j, label='J', color='green', linewidth=1)
    ax2.axhline(y=80, color='red', linestyle='--', alpha=0.5, label='超买线(80)')
    ax2.axhline(y=20, color='green', linestyle='--', alpha=0.5, label='超卖线(20)')
    ax2.set_title('KDJ指标')
    ax2.set_ylabel('KDJ值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 绘制MACD指标
    ax3.plot(range(len(data)), macd_line, label='MACD线', color='blue', linewidth=1)
    ax3.plot(range(len(data)), signal_line, label='信号线', color='red', linewidth=1)
    ax3.bar(range(len(data)), macd_histogram, label='MACD柱', 
            color=['red' if x > 0 else 'green' for x in macd_histogram], alpha=0.6)
    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=1)
    
    # 标记MACD买入信号点
    if not trade_signals.empty:
        for _, signal in trade_signals.iterrows():
            if isinstance(signal['date'], int):
                entry_idx = signal['date']
            else:
                try:
                    entry_idx = data.index.get_loc(pd.to_datetime(signal['date']))
                except:
                    continue
            
            if 0 <= entry_idx < len(data):
                ax3.scatter(entry_idx, signal['macd_line'], 
                           color='yellow', s=80, marker='^', 
                           edgecolors='black', linewidth=2, zorder=5)

    ax3.set_title('MACD指标')
    ax3.set_ylabel('MACD值')
    ax3.set_xlabel('时间')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 设置x轴标签
    x_ticks = range(0, len(data), max(1, len(data)//10))
    x_labels = [data.index[i].strftime('%Y-%m-%d') for i in x_ticks]
    ax3.set_xticks(x_ticks)
    ax3.set_xticklabels(x_labels, rotation=45)

    plt.tight_layout()
    plt.show()

    return k, d, j, ema12, ema26, macd_line, signal_line, macd_histogram

def save_data_to_excel(stock_data, trade_signals, filename="stock_analysis.xlsx"):
    """
    将股票数据、技术指标和交易信号保存到Excel文件
    """
    # 创建数据副本
    data_copy = stock_data.copy()
    
    # 计算所有技术指标
    k, d, j = calculate_kdj(data_copy)
    ema12 = calculate_ema(data_copy['close'], 12)
    ema26 = calculate_ema(data_copy['close'], 26)
    macd_line, signal_line, macd_histogram = calculate_macd(data_copy)
    atr = calculate_atr(data_copy)
    
    # 添加指标到数据中
    data_copy['K'] = k
    data_copy['D'] = d
    data_copy['J'] = j
    data_copy['EMA12'] = ema12
    data_copy['EMA26'] = ema26
    data_copy['MACD_Line'] = macd_line
    data_copy['Signal_Line'] = signal_line
    data_copy['MACD_Histogram'] = macd_histogram
    data_copy['ATR'] = atr
    
    # 使用ExcelWriter保存多个工作表
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 保存股票数据和技术指标
        data_copy.to_excel(writer, sheet_name='股票数据与指标', index=False)
        
        # 保存交易信号
        if not trade_signals.empty:
            trade_signals.to_excel(writer, sheet_name='交易信号', index=False)
        else:
            # 创建空的交易信号表
            empty_signals = pd.DataFrame(columns=['date', 'entry_price', 'stop_loss', 'take_profit', 'atr', 'body_size', 'macd_line', 'macd_histogram'])
            empty_signals.to_excel(writer, sheet_name='交易信号', index=False)
    
    print(f"数据已保存到 {filename}")
    print(f"包含工作表: '股票数据与指标', '交易信号'")

# 这些代码应该在batch_analyze_stocks函数中执行，而不是在模块级别
