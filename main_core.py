#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
龙头股技术分析主程序
实现双顶、KDJ入场信号、MACD背离识别和绘图功能
"""

import os
import sys
from datetime import datetime, timedelta

# 检查并导入必要的包
try:
    import pandas as pd
    import numpy as np
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    import baostock as bs
    print("✓ 所有依赖包导入成功")
except ImportError as e:
    print(f"✗ 导入依赖包失败: {e}")
    print("请安装必要的包: pip install pandas numpy matplotlib baostock openpyxl")
    sys.exit(1)

# 设置中文字体
def set_chinese_font():
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False

def load_stock_codes():
    """读取股票代码和涨停前一天日期"""
    try:
        df = pd.read_excel('stock_codes_with_prev_day.xlsx')
        print(f"✓ 成功读取股票池，共 {len(df)} 只股票")
        print(f"列名: {df.columns.tolist()}")
        return df
    except Exception as e:
        print(f"✗ 读取股票池文件失败: {e}")
        return None

def get_stock_data(stock_code, end_date, days=200):
    """获取股票数据"""
    try:
        print(f"正在获取 {stock_code} 数据...")
        lg = bs.login()
        
        if lg.error_code != '0':
            print(f"baostock登录失败: {lg.error_msg}")
            return None
        
        # 计算开始日期
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        start_dt = end_dt - timedelta(days=days*2)
        start_date = start_dt.strftime('%Y-%m-%d')
        
        rs = bs.query_history_k_data_plus(
            stock_code,
            "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
            start_date=start_date,
            end_date=end_date,
            frequency="d",
            adjustflag="2"
        )
        
        if rs.error_code != '0':
            print(f"查询股票 {stock_code} 失败: {rs.error_msg}")
            bs.logout()
            return None
        
        data_list = []
        while (rs.error_code == '0') & rs.next():
            data_list.append(rs.get_row_data())
        
        bs.logout()
        
        if not data_list:
            return None
        
        result = pd.DataFrame(data_list, columns=rs.fields)
        
        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn', 'pctChg']
        for col in numeric_columns:
            if col in result.columns:
                result[col] = pd.to_numeric(result[col], errors='coerce')
        
        result = result.tail(days).reset_index(drop=True)
        print(f"获取完成，共 {len(result)} 条数据")
        return result
        
    except Exception as e:
        print(f"获取股票 {stock_code} 数据失败: {e}")
        try:
            bs.logout()
        except:
            pass
        return None

def calculate_macd(data, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    ema_fast = data['close'].ewm(span=fast).mean()
    ema_slow = data['close'].ewm(span=slow).mean()
    
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal).mean()
    macd_histogram = macd_line - signal_line
    
    return macd_line, signal_line, macd_histogram

def calculate_kdj(data, n=9, m1=3, m2=3):
    """计算KDJ指标"""
    low_n = data['low'].rolling(window=n).min()
    high_n = data['high'].rolling(window=n).max()
    rsv = (data['close'] - low_n) / (high_n - low_n) * 100
    
    k = rsv.ewm(alpha=1/m1).mean()
    d = k.ewm(alpha=1/m2).mean()
    j = 3 * k - 2 * d
    
    return k, d, j

def calculate_ma(data, periods=[5, 10, 20, 60]):
    """计算移动平均线"""
    ma_dict = {}
    for period in periods:
        ma_dict[f'MA{period}'] = data['close'].rolling(window=period).mean()
    return ma_dict

def detect_double_top(data, min_gap=15):
    """识别双顶形态"""
    try:
        highs = []
        for i in range(2, len(data)-2):
            if (data['high'].iloc[i] > data['high'].iloc[i-1] and 
                data['high'].iloc[i] > data['high'].iloc[i+1] and
                data['high'].iloc[i] > data['high'].iloc[i-2] and 
                data['high'].iloc[i] > data['high'].iloc[i+2]):
                highs.append((i, data['high'].iloc[i], data['date'].iloc[i]))
        
        if len(highs) < 2:
            return False, None, None, None
        
        for i in range(len(highs)-1):
            for j in range(i+1, len(highs)):
                idx1, price1, date1 = highs[i]
                idx2, price2, date2 = highs[j]
                
                if idx2 - idx1 >= min_gap:
                    price_diff = abs(price1 - price2) / max(price1, price2)
                    if price_diff <= 0.03:
                        min_between = data['low'].iloc[idx1:idx2+1].min()
                        if min_between < min(price1, price2) * 0.95:
                            connect_price = (price1 + price2) / 2
                            return True, date1, date2, connect_price
        
        return False, None, None, None
        
    except Exception as e:
        print(f"双顶识别失败: {e}")
        return False, None, None, None

def detect_kdj_entry(data):
    """识别KDJ入场信号"""
    try:
        k, d, j = calculate_kdj(data)
        vol_ma = data['volume'].rolling(window=20).mean()
        signals = []
        
        for i in range(30, len(data)-2):
            # 检查前期放量上涨
            volume_surge = False
            price_rise = False
            
            for start in range(max(0, i-15), i-5):
                surge_days = 0
                rise_days = 0
                for day in range(start, start+5):
                    if day < len(data) and day >= 0:
                        if data['volume'].iloc[day] > vol_ma.iloc[day] * 1.2:
                            surge_days += 1
                        if data['close'].iloc[day] > data['close'].iloc[day-1]:
                            rise_days += 1
                
                if surge_days >= 3 and rise_days >= 3:
                    volume_surge = True
                    price_rise = True
                    break
            
            if not (volume_surge and price_rise):
                continue
            
            # 检查KDJ死叉
            if i >= 1 and k.iloc[i-1] > d.iloc[i-1] and k.iloc[i] < d.iloc[i]:
                # 检查KD差距连续2日缩小但未金叉
                if i+2 < len(data):
                    kd_diff_1 = abs(k.iloc[i] - d.iloc[i])
                    kd_diff_2 = abs(k.iloc[i+1] - d.iloc[i+1])
                    kd_diff_3 = abs(k.iloc[i+2] - d.iloc[i+2])
                    
                    if (kd_diff_2 < kd_diff_1 and kd_diff_3 < kd_diff_2 and 
                        k.iloc[i+2] < d.iloc[i+2]):
                        
                        entry_date = data['date'].iloc[i+2]
                        signals.append({
                            'entry_date': entry_date,
                            'entry_price': data['close'].iloc[i+2],
                            'k_value': k.iloc[i+2],
                            'd_value': d.iloc[i+2],
                            'death_cross_date': data['date'].iloc[i]
                        })
        
        if signals:
            return True, signals[-1]['entry_date'], signals[-1]
        else:
            return False, None, None
            
    except Exception as e:
        print(f"KDJ信号识别失败: {e}")
        return False, None, None

def detect_macd_divergence(data):
    """MACD背离识别（使用MACD_analysis.py的逻辑）"""
    try:
        macd_line, signal_line, macd_histogram = calculate_macd(data)
        
        data_copy = data.copy()
        data_copy['macd_line'] = macd_line
        data_copy['signal_line'] = signal_line
        data_copy['macd_histogram'] = macd_histogram
        
        # 寻找第一个峰值（倒数第3或第4个MACD柱）
        first_peak_found = False
        first_peak_idx = None
        
        for i in [3, 4]:
            if i >= len(data_copy):
                continue
            
            if data_copy['macd_histogram'].iloc[-i] >= 0:
                continue
            
            if (i >= 2 and i < len(data_copy) - 2 and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i-1] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i+1] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i-2] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i+2]):
                
                first_peak_found = True
                first_peak_idx = i
                break
        
        if not first_peak_found:
            return False, None, None
        
        # 寻找第二个峰值
        second_peak_found = False
        prev_peak_idx = None
        
        for j in range(first_peak_idx + 2, min(first_peak_idx + 50, len(data_copy) - 3)):
            if data_copy['macd_histogram'].iloc[-j] > 0:
                return False, None, None
            
            if (data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j-1] and
                data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j+1] and
                data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j-2] and
                data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j+2] and
                data_copy['macd_histogram'].iloc[-j] < 0):
                prev_peak_idx = j
                second_peak_found = True
                break
        
        if not second_peak_found:
            return False, None, None
        
        # 检查背离条件
        current_macd = data_copy['macd_histogram'].iloc[-first_peak_idx]
        prev_macd = data_copy['macd_histogram'].iloc[-prev_peak_idx]
        current_price = data_copy['low'].iloc[-first_peak_idx]
        prev_price = data_copy['low'].iloc[-prev_peak_idx]
        
        if not (current_macd > prev_macd and current_price < prev_price):
            return False, None, None
        
        # 检查最新MACD柱绝对值是否减少
        if len(data_copy) < 2:
            return False, None, None
        
        latest_macd_abs = abs(data_copy['macd_histogram'].iloc[-1])
        prev_day_macd_abs = abs(data_copy['macd_histogram'].iloc[-2])
        
        if latest_macd_abs >= prev_day_macd_abs:
            return False, None, None
        
        # 生成信号
        entry_idx = len(data_copy) - first_peak_idx
        entry_date = data_copy['date'].iloc[entry_idx]
        
        signal_info = {
            'entry_date': entry_date,
            'entry_price': data_copy['close'].iloc[entry_idx],
            'first_peak_idx': len(data_copy) - first_peak_idx,
            'second_peak_idx': len(data_copy) - prev_peak_idx,
            'macd_line': data_copy['macd_line'].iloc[entry_idx],
            'macd_histogram': data_copy['macd_histogram'].iloc[entry_idx]
        }
        
        return True, entry_date, signal_info
        
    except Exception as e:
        print(f"MACD背离识别失败: {e}")
        return False, None, None

if __name__ == "__main__":
    print("龙头股技术分析程序")
    print("="*50)
    
    # 测试读取Excel文件
    stock_df = load_stock_codes()
    if stock_df is not None:
        print("✓ Excel文件读取成功，程序可以正常运行")
        print("请运行完整版main.py进行分析")
    else:
        print("✗ Excel文件读取失败，请检查文件")
